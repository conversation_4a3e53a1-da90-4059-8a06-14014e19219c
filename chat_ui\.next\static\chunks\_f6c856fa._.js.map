{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/CommandBadge.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\ninterface CommandBadgeProps {\r\n  command: \"generate image\" | \"scrape website\";\r\n}\r\n\r\nconst commandStyles = {\r\n  \"generate image\": {\r\n    label: \"Image\",\r\n    bgColor: \"bg-blue-500/80\",\r\n  },\r\n  \"scrape website\": {\r\n    label: \"Scrape\",\r\n    bgColor: \"bg-green-500/80\",\r\n  },\r\n};\r\n\r\nexport default function CommandBadge({ command }: CommandBadgeProps) {\r\n  const { label, bgColor } = commandStyles[command];\r\n\r\n  return (\r\n    <span\r\n      className={`glass-card inline-flex items-center px-2.5 py-0.5 rounded-sm text-xs font-medium text-white border-0 ${bgColor}`}\r\n    >\r\n      {label}\r\n    </span>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAMA,MAAM,gBAAgB;IACpB,kBAAkB;QAChB,OAAO;QACP,SAAS;IACX;IACA,kBAAkB;QAChB,OAAO;QACP,SAAS;IACX;AACF;AAEe,SAAS,aAAa,KAA8B;QAA9B,EAAE,OAAO,EAAqB,GAA9B;IACnC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,aAAa,CAAC,QAAQ;IAEjD,qBACE,6LAAC;QACC,WAAW,AAAC,wGAA+G,OAAR;kBAElH;;;;;;AAGP;KAVwB", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/ChatInput.tsx"], "sourcesContent": ["import { useState, useRef, useEffect } from \"react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { FaPaperPlane } from \"react-icons/fa\";\r\nimport CommandBadge from \"./CommandBadge\";\r\n\r\ninterface ChatInputProps {\r\n  onSendMessage: (message: string) => void;\r\n  isLoading: boolean;\r\n}\r\n\r\ntype Command = \"generate image\" | \"scrape website\";\r\n\r\nexport default function ChatInput({ onSendMessage, isLoading }: ChatInputProps) {\r\n  const [input, setInput] = useState(\"\");\r\n  const [showMenu, setShowMenu] = useState(false);\r\n  const [activeCommand, setActiveCommand] = useState<Command | null>(null);\r\n  const [focusedIndex, setFocusedIndex] = useState(0);\r\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\r\n  const badgeRef = useRef<HTMLSpanElement>(null);\r\n  const commands: Command[] = [\"generate image\", \"scrape website\"];\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\r\n    const value = e.target.value;\r\n    setInput(value);\r\n\r\n    if (activeCommand) {\r\n      return;\r\n    }\r\n\r\n    if (value === \"/\") {\r\n      setShowMenu(true);\r\n      setFocusedIndex(0);\r\n    } else {\r\n      setShowMenu(false);\r\n    }\r\n  };\r\n\r\n  const handleSendMessage = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!input.trim() && !activeCommand) return;\r\n\r\n    const message = activeCommand ? `/${activeCommand} ${input}` : input;\r\n    onSendMessage(message);\r\n    setInput(\"\");\r\n    setActiveCommand(null);\r\n    setShowMenu(false);\r\n  };\r\n\r\n  const handleOptionClick = (option: Command) => {\r\n    setActiveCommand(option);\r\n    setInput(\"\");\r\n    setShowMenu(false);\r\n    setTimeout(() => {\r\n      textareaRef.current?.focus();\r\n    }, 0);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (textareaRef.current) {\r\n      textareaRef.current.style.height = \"auto\";\r\n      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;\r\n    }\r\n  }, [input, activeCommand]);\r\n\r\n  useEffect(() => {\r\n    if (textareaRef.current && badgeRef.current) {\r\n      const badgeWidth = badgeRef.current.offsetWidth;\r\n      textareaRef.current.style.textIndent = `${badgeWidth + 8}px`;\r\n    } else if (textareaRef.current) {\r\n      textareaRef.current.style.textIndent = \"0px\";\r\n    }\r\n  }, [activeCommand]);\r\n\r\n  return (\r\n    <footer className=\"glass-header py-4 px-6 border-t-0 border-b-0\">\r\n      <div className=\"relative\">\r\n        {showMenu && (\r\n          <div className=\"absolute bottom-full mb-2 w-full glass-card rounded-lg shadow-2xl z-10\">\r\n            <ul>\r\n              {commands.map((command, index) => (\r\n                <li\r\n                  key={command}\r\n                  className={`px-4 py-2 cursor-pointer rounded-lg transition-all duration-200 ${\r\n                    index === focusedIndex\r\n                      ? \"bg-primary/20 text-primary\"\r\n                      : \"text-foreground hover:bg-white/10\"\r\n                  }`}\r\n                  onClick={() => handleOptionClick(command)}\r\n                  onMouseEnter={() => setFocusedIndex(index)}\r\n                >\r\n                  {command.charAt(0).toUpperCase() + command.slice(1)}\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n        )}\r\n        <form onSubmit={handleSendMessage} className=\"relative\">\r\n          <div className=\"relative flex items-center\">\r\n            {activeCommand && (\r\n              <div\r\n                ref={badgeRef as any}\r\n                className=\"absolute top-1.5 left-3\"\r\n              >\r\n                <CommandBadge command={activeCommand} />\r\n              </div>\r\n            )}\r\n            <Textarea\r\n              ref={textareaRef}\r\n              placeholder={\r\n                activeCommand ? \"\" : \"Type your message or / for commands\"\r\n              }\r\n              className=\"glass-intense pr-16 rounded-lg text-foreground placeholder:text-muted-foreground focus:ring-primary/50 focus:border-primary/50 w-full border-0 resize-none\"\r\n              value={input}\r\n              onChange={handleInputChange}\r\n              onKeyDown={(e) => {\r\n                if (showMenu) {\r\n                  if (e.key === \"ArrowUp\") {\r\n                    e.preventDefault();\r\n                    setFocusedIndex((prevIndex) =>\r\n                      prevIndex > 0 ? prevIndex - 1 : commands.length - 1\r\n                    );\r\n                  } else if (e.key === \"ArrowDown\") {\r\n                    e.preventDefault();\r\n                    setFocusedIndex((prevIndex) =>\r\n                      prevIndex < commands.length - 1 ? prevIndex + 1 : 0\r\n                    );\r\n                  } else if (e.key === \"Enter\") {\r\n                    e.preventDefault();\r\n                    handleOptionClick(commands[focusedIndex]);\r\n                  }\r\n                } else if (e.key === \"Enter\" && !e.shiftKey) {\r\n                  handleSendMessage(e);\r\n                }\r\n\r\n                if (e.key === \"Backspace\" && input === \"\" && activeCommand) {\r\n                  setActiveCommand(null);\r\n                }\r\n              }}\r\n              rows={1}\r\n            />\r\n            <Button\r\n              type=\"submit\"\r\n              className=\"glass-intense absolute top-1/2 right-4 -translate-y-1/2 bg-primary/80 hover:bg-primary text-primary-foreground border-0 disabled:opacity-50\"\r\n              disabled={isLoading}\r\n            >\r\n              <FaPaperPlane className=\"w-5 h-5\" />\r\n            </Button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </footer>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;;AASe,SAAS,UAAU,KAA4C;QAA5C,EAAE,aAAa,EAAE,SAAS,EAAkB,GAA5C;;IAChC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IAChD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAmB;IACzC,MAAM,WAAsB;QAAC;QAAkB;KAAiB;IAEhE,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,SAAS;QAET,IAAI,eAAe;YACjB;QACF;QAEA,IAAI,UAAU,KAAK;YACjB,YAAY;YACZ,gBAAgB;QAClB,OAAO;YACL,YAAY;QACd;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,eAAe;QAErC,MAAM,UAAU,gBAAgB,AAAC,IAAoB,OAAjB,eAAc,KAAS,OAAN,SAAU;QAC/D,cAAc;QACd,SAAS;QACT,iBAAiB;QACjB,YAAY;IACd;IAEA,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;QACjB,SAAS;QACT,YAAY;QACZ,WAAW;gBACT;aAAA,uBAAA,YAAY,OAAO,cAAnB,2CAAA,qBAAqB,KAAK;QAC5B,GAAG;IACL;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,YAAY,OAAO,EAAE;gBACvB,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;gBACnC,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,AAAC,GAAmC,OAAjC,YAAY,OAAO,CAAC,YAAY,EAAC;YACzE;QACF;8BAAG;QAAC;QAAO;KAAc;IAEzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,YAAY,OAAO,IAAI,SAAS,OAAO,EAAE;gBAC3C,MAAM,aAAa,SAAS,OAAO,CAAC,WAAW;gBAC/C,YAAY,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,AAAC,GAAiB,OAAf,aAAa,GAAE;YAC3D,OAAO,IAAI,YAAY,OAAO,EAAE;gBAC9B,YAAY,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG;YACzC;QACF;8BAAG;QAAC;KAAc;IAElB,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;gBACZ,0BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;kCACE,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;gCAEC,WAAW,AAAC,mEAIX,OAHC,UAAU,eACN,+BACA;gCAEN,SAAS,IAAM,kBAAkB;gCACjC,cAAc,IAAM,gBAAgB;0CAEnC,QAAQ,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,KAAK,CAAC;+BAT5C;;;;;;;;;;;;;;;8BAef,6LAAC;oBAAK,UAAU;oBAAmB,WAAU;8BAC3C,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,+BACC,6LAAC;gCACC,KAAK;gCACL,WAAU;0CAEV,cAAA,6LAAC,sIAAA,CAAA,UAAY;oCAAC,SAAS;;;;;;;;;;;0CAG3B,6LAAC,gIAAA,CAAA,WAAQ;gCACP,KAAK;gCACL,aACE,gBAAgB,KAAK;gCAEvB,WAAU;gCACV,OAAO;gCACP,UAAU;gCACV,WAAW,CAAC;oCACV,IAAI,UAAU;wCACZ,IAAI,EAAE,GAAG,KAAK,WAAW;4CACvB,EAAE,cAAc;4CAChB,gBAAgB,CAAC,YACf,YAAY,IAAI,YAAY,IAAI,SAAS,MAAM,GAAG;wCAEtD,OAAO,IAAI,EAAE,GAAG,KAAK,aAAa;4CAChC,EAAE,cAAc;4CAChB,gBAAgB,CAAC,YACf,YAAY,SAAS,MAAM,GAAG,IAAI,YAAY,IAAI;wCAEtD,OAAO,IAAI,EAAE,GAAG,KAAK,SAAS;4CAC5B,EAAE,cAAc;4CAChB,kBAAkB,QAAQ,CAAC,aAAa;wCAC1C;oCACF,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;wCAC3C,kBAAkB;oCACpB;oCAEA,IAAI,EAAE,GAAG,KAAK,eAAe,UAAU,MAAM,eAAe;wCAC1D,iBAAiB;oCACnB;gCACF;gCACA,MAAM;;;;;;0CAER,6LAAC,8HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;gCACV,UAAU;0CAEV,cAAA,6LAAC,iJAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC;GA5IwB;KAAA", "debugId": null}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/ChatMessage.tsx"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>Cir<PERSON> } from \"react-icons/fa\";\r\nimport { Download, Copy, Check } from \"lucide-react\";\r\nimport React, { useState } from \"react\";\r\nimport ReactMarkdown from \"react-markdown\";\r\nimport { downloadImage } from \"@/lib/utils\";\r\nimport { Prism as Syntax<PERSON>ighlighter } from \"react-syntax-highlighter\";\r\nimport { vscDarkPlus } from \"react-syntax-highlighter/dist/esm/styles/prism\";\r\nimport remarkGfm from \"remark-gfm\";\r\n\r\ninterface ChatMessageProps {\r\n  message: {\r\n    content: string;\r\n    type: \"human\" | \"ai\";\r\n    image_url?: string;\r\n    status?: \"processing\" | \"complete\";\r\n  };\r\n}\r\n\r\nexport default function ChatMessage({ message }: ChatMessageProps) {\r\n  const isHuman = message.type === \"human\";\r\n  const [copied, setCopied] = useState(false);\r\n\r\n  const handleCopy = (text: string) => {\r\n    navigator.clipboard.writeText(text);\r\n    setCopied(true);\r\n    setTimeout(() => setCopied(false), 2000);\r\n  };\r\n  return (\r\n    <div className={`flex items-start gap-4 ${isHuman ? \"justify-end\" : \"\"}`}>\r\n      {!isHuman && (\r\n        <div className=\"rounded-full glass-intense w-8 h-8 flex items-center justify-center\">\r\n          <FaRobot className=\"w-5 h-5 text-primary\" />\r\n        </div>\r\n      )}\r\n      <div\r\n        className={`${\r\n          isHuman\r\n            ? \"glass-intense bg-primary/20 border-primary/30\"\r\n            : \"glass-intense\"\r\n        } rounded-lg p-4 max-w-[75%] relative group`}\r\n      >\r\n        {message.status && (\r\n          <div className=\"absolute -top-2 -right-2 bg-background/80 backdrop-blur-sm rounded-full p-1 border border-border/50\">\r\n            {message.status === \"processing\" && (\r\n              <FaSpinner className=\"animate-spin h-4 w-4 text-primary\" />\r\n            )}\r\n            {message.status === \"complete\" && (\r\n              <FaCheckCircle className=\"h-4 w-4 text-green-500\" />\r\n            )}\r\n          </div>\r\n        )}\r\n        <button\r\n          onClick={() => handleCopy(message.content)}\r\n          className=\"glass-button absolute top-2 right-2 text-foreground p-2 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-200 border-0\"\r\n        >\r\n          {copied ? (\r\n            <Check className=\"h-5 w-5 text-green-500\" />\r\n          ) : (\r\n            <Copy className=\"h-5 w-5\" />\r\n          )}\r\n        </button>\r\n        <ReactMarkdown\r\n          remarkPlugins={[remarkGfm]}\r\n          components={{\r\n            code({ node, inline, className, children, ...props }: any) {\r\n              const match = /language-(\\w+)/.exec(className || \"\");\r\n              return !inline && match ? (\r\n                <div className=\"relative\">\r\n                  <button\r\n                    onClick={() => handleCopy(String(children))}\r\n                    className=\"glass-button absolute top-2 right-2 text-foreground p-2 rounded-full border-0\"\r\n                  >\r\n                    {copied ? (\r\n                      <Check className=\"h-5 w-5 text-green-500\" />\r\n                    ) : (\r\n                      <Copy className=\"h-5 w-5\" />\r\n                    )}\r\n                  </button>\r\n                  <SyntaxHighlighter\r\n                    style={vscDarkPlus as any}\r\n                    language={match[1]}\r\n                    PreTag=\"div\"\r\n                    {...props}\r\n                  >\r\n                    {String(children).replace(/\\n$/, \"\")}\r\n                  </SyntaxHighlighter>\r\n                </div>\r\n              ) : (\r\n                <code className={className} {...props}>\r\n                  {children}\r\n                </code>\r\n              );\r\n            },\r\n          }}\r\n        >\r\n          {message.content}\r\n        </ReactMarkdown>\r\n        {message.image_url && (\r\n          <div className=\"mt-2 relative group\">\r\n            <img\r\n              src={message.image_url}\r\n              alt=\"Generated image\"\r\n              className=\"rounded-lg max-w-full h-auto\"\r\n            />\r\n            <button\r\n              onClick={() => downloadImage(message.image_url!)}\r\n              className=\"glass-button absolute top-2 right-2 text-foreground p-2 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-200 border-0\"\r\n            >\r\n              <Download className=\"h-5 w-5\" />\r\n            </button>\r\n          </div>\r\n        )}\r\n      </div>\r\n      {isHuman && (\r\n        <div className=\"rounded-full glass-intense w-8 h-8 flex items-center justify-center\">\r\n          <FaUser className=\"w-5 h-5 text-primary\" />\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAWe,SAAS,YAAY,KAA6B;QAA7B,EAAE,OAAO,EAAoB,GAA7B;;IAClC,MAAM,UAAU,QAAQ,IAAI,KAAK;IACjC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,aAAa,CAAC;QAClB,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,UAAU;QACV,WAAW,IAAM,UAAU,QAAQ;IACrC;IACA,qBACE,6LAAC;QAAI,WAAW,AAAC,0BAAsD,OAA7B,UAAU,gBAAgB;;YACjE,CAAC,yBACA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iJAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;;0BAGvB,6LAAC;gBACC,WAAW,AAAC,GAIX,OAHC,UACI,kDACA,iBACL;;oBAEA,QAAQ,MAAM,kBACb,6LAAC;wBAAI,WAAU;;4BACZ,QAAQ,MAAM,KAAK,8BAClB,6LAAC,iJAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAEtB,QAAQ,MAAM,KAAK,4BAClB,6LAAC,iJAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;;kCAI/B,6LAAC;wBACC,SAAS,IAAM,WAAW,QAAQ,OAAO;wBACzC,WAAU;kCAET,uBACC,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;iDAEjB,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAGpB,6LAAC,2LAAA,CAAA,UAAa;wBACZ,eAAe;4BAAC,gJAAA,CAAA,UAAS;yBAAC;wBAC1B,YAAY;4BACV,MAAK,KAAoD;oCAApD,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAY,GAApD;gCACH,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;gCACjD,OAAO,CAAC,UAAU,sBAChB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,WAAW,OAAO;4CACjC,WAAU;sDAET,uBACC,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;uEAEjB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAGpB,6LAAC,6MAAA,CAAA,QAAiB;4CAChB,OAAO,oPAAA,CAAA,cAAW;4CAClB,UAAU,KAAK,CAAC,EAAE;4CAClB,QAAO;4CACN,GAAG,KAAK;sDAER,OAAO,UAAU,OAAO,CAAC,OAAO;;;;;;;;;;;2DAIrC,6LAAC;oCAAK,WAAW;oCAAY,GAAG,KAAK;8CAClC;;;;;;4BAGP;wBACF;kCAEC,QAAQ,OAAO;;;;;;oBAEjB,QAAQ,SAAS,kBAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,KAAK,QAAQ,SAAS;gCACtB,KAAI;gCACJ,WAAU;;;;;;0CAEZ,6LAAC;gCACC,SAAS,IAAM,CAAA,GAAA,+GAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,SAAS;gCAC9C,WAAU;0CAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAK3B,yBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iJAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAK5B;GAtGwB;KAAA", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/ChatMessages.tsx"], "sourcesContent": ["import { useEffect, useRef } from \"react\";\r\nimport { FaRobot } from \"react-icons/fa\";\r\nimport ChatMessage from \"./ChatMessage\";\r\n\r\ninterface Message {\r\n  content: string;\r\n  type: \"human\" | \"ai\";\r\n  status?: \"processing\" | \"complete\";\r\n}\r\n\r\ninterface ChatMessagesProps {\r\n  messages: Message[];\r\n  isLoading?: boolean;\r\n}\r\n\r\nexport default function ChatMessages({ messages, isLoading }: ChatMessagesProps) {\r\n  const messagesEndRef = useRef<HTMLDivElement | null>(null);\r\n\r\n  const scrollToBottom = () => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages, isLoading]);\r\n\r\n  return (\r\n    <main className=\"flex-1 overflow-y-auto p-6 bg-transparent\">\r\n      <div className=\"space-y-4\">\r\n        {messages.map((message, index) => (\r\n          <ChatMessage key={index} message={message} />\r\n        ))}\r\n        {isLoading && (\r\n          <div className=\"flex items-start gap-4\">\r\n            <div className=\"rounded-full glass-intense w-8 h-8 flex items-center justify-center glass-float\">\r\n              <FaRobot className=\"w-5 h-5 text-primary\" />\r\n            </div>\r\n            <div className=\"glass-intense rounded-lg p-4 max-w-[75%] flex items-center gap-3 glass-shimmer\">\r\n              <div className=\"flex items-center gap-1\">\r\n                <div className=\"w-2 h-2 bg-primary rounded-full animate-bounce [animation-delay:-0.3s]\"></div>\r\n                <div className=\"w-2 h-2 bg-primary rounded-full animate-bounce [animation-delay:-0.15s]\"></div>\r\n                <div className=\"w-2 h-2 bg-primary rounded-full animate-bounce\"></div>\r\n              </div>\r\n              <span className=\"text-muted-foreground text-sm\">AI is thinking...</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n        <div ref={messagesEndRef} />\r\n      </div>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;;AAae,SAAS,aAAa,KAA0C;QAA1C,EAAE,QAAQ,EAAE,SAAS,EAAqB,GAA1C;;IACnC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IAErD,MAAM,iBAAiB;YACrB;SAAA,0BAAA,eAAe,OAAO,cAAtB,8CAAA,wBAAwB,cAAc,CAAC;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG;QAAC;QAAU;KAAU;IAExB,qBACE,6LAAC;QAAK,WAAU;kBACd,cAAA,6LAAC;YAAI,WAAU;;gBACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,qIAAA,CAAA,UAAW;wBAAa,SAAS;uBAAhB;;;;;gBAEnB,2BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,iJAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAErB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;8BAItD,6LAAC;oBAAI,KAAK;;;;;;;;;;;;;;;;;AAIlB;GApCwB;KAAA", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 795, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/ChatOption.tsx"], "sourcesContent": ["import { Card } from \"../ui/card\";\r\nimport { FaScrewdriverWrench } from \"react-icons/fa6\";\r\nimport { LuImagePlus } from \"react-icons/lu\";\r\nimport { FaSignsPost } from \"react-icons/fa6\";\r\nimport Link from \"next/link\";\r\n\r\nconst data = [\r\n  {\r\n    title: \"Scrape Website\",\r\n    description: \"Scrape website data and extract information.\",\r\n    icon: FaScrewdriverWrench,\r\n    param: \"scrape_website\",\r\n  },\r\n  {\r\n    title: \"Generate Image\",\r\n    description: \"Generate Image from text.\",\r\n    icon: LuImagePlus,\r\n    param: \"generate_image\",\r\n  },\r\n  {\r\n    title: \"Generate Poster Images\",\r\n    description: \"Generate poster images from text.\",\r\n    icon: FaSignsPost,\r\n    param: \"generate_poster_images\",\r\n  },\r\n];\r\n\r\nexport default function ChatOptions() {\r\n  return (\r\n    <div className=\"flex items-center justify-center flex-wrap h-full gap-4 p-6\">\r\n      {data.map((item, index) => (\r\n        <Link href={`/dashboard/chat/new?${item.param}`} passHref key={index}>\r\n          <Card className=\"glass-intense p-6 text-foreground gap-3 hover:scale-105 transition-all duration-300 cursor-pointer group border-0 glass-shimmer\">\r\n            <div className=\"flex flex-col items-center text-center space-y-3\">\r\n              <div className=\"glass-card p-3 rounded-full glass-float\">\r\n                <item.icon className=\"h-6 w-6 text-primary\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm font-medium\">{item.title}</p>\r\n                <p className=\"text-xs text-muted-foreground mt-1\">{item.description}</p>\r\n              </div>\r\n            </div>\r\n          </Card>\r\n        </Link>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;;AAEA,MAAM,OAAO;IACX;QACE,OAAO;QACP,aAAa;QACb,MAAM,kJAAA,CAAA,sBAAmB;QACzB,OAAO;IACT;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,iJAAA,CAAA,cAAW;QACjB,OAAO;IACT;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,kJAAA,CAAA,cAAW;QACjB,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACZ,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,AAAC,uBAAiC,OAAX,KAAK,KAAK;gBAAI,QAAQ;0BACvD,cAAA,6LAAC,4HAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;;;;;;0CAEvB,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDAAuB,KAAK,KAAK;;;;;;kDAC9C,6LAAC;wCAAE,WAAU;kDAAsC,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;eARZ;;;;;;;;;;AAgBvE;KApBwB", "debugId": null}}, {"offset": {"line": 912, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/compound/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  createContext,\r\n  useContext,\r\n  useState,\r\n  useEffect,\r\n  ReactNode,\r\n} from \"react\";\r\nimport { useParams, useRouter } from \"next/navigation\";\r\nimport { useSendMessageMutation, useGetChatHistoryQuery } from \"@/store/api\";\r\nimport ChatInput from \"@/components/chat/ChatInput\";\r\nimport ChatMessages from \"@/components/chat/ChatMessages\";\r\nimport ChatLoading from \"@/components/chat/ChatLoading\";\r\nimport ChatOptions from \"@/components/chat/ChatOption\";\r\n\r\ninterface ChatContextType {\r\n  messages: Array<{ content: string; type: \"human\" | \"ai\" }>;\r\n  isSending: boolean;\r\n  isHistoryLoading: boolean;\r\n  handleSendMessage: (message: string) => void;\r\n  chatId: string | null;\r\n}\r\n\r\nconst ChatContext = createContext<ChatContextType | undefined>(undefined);\r\n\r\nexport const useChat = () => {\r\n  const context = useContext(ChatContext);\r\n  if (!context) {\r\n    throw new Error(\"useChat must be used within a ChatProvider\");\r\n  }\r\n  return context;\r\n};\r\n\r\ninterface ChatProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function ChatProvider({ children }: ChatProviderProps) {\r\n  const params = useParams();\r\n  const router = useRouter();\r\n  const id = params.id as string;\r\n  const [messages, setMessages] = useState<\r\n    Array<{ content: string; type: \"human\" | \"ai\" }>\r\n  >([]);\r\n  const [chatId, setChatId] = useState<string | null>(id === \"new\" ? null : id);\r\n  const [sendMessage, { isLoading: isSending }] = useSendMessageMutation();\r\n  const {\r\n    data: chatHistoryData,\r\n    refetch,\r\n    isLoading: isHistoryLoading,\r\n  } = useGetChatHistoryQuery(chatId!, { skip: !chatId });\r\n\r\n  useEffect(() => {\r\n    if (id === \"new\") {\r\n      setChatId(null);\r\n      setMessages([]);\r\n    } else {\r\n      setChatId(id);\r\n      refetch();\r\n    }\r\n  }, [id, refetch]);\r\n\r\n  useEffect(() => {\r\n    if (chatHistoryData) {\r\n      setMessages(\r\n        chatHistoryData.messages as Array<{\r\n          content: string;\r\n          type: \"human\" | \"ai\";\r\n        }>\r\n      );\r\n    }\r\n  }, [chatHistoryData]);\r\n\r\n  const handleSendMessage = async (message: string) => {\r\n    if (!message.trim()) return;\r\n\r\n    const newMessages = [\r\n      ...messages,\r\n      { content: message, type: \"human\" as const },\r\n    ];\r\n    setMessages(newMessages);\r\n\r\n    if (chatId) {\r\n      const { data } = await sendMessage({ message, chat_id: chatId });\r\n      if (data && data.response) {\r\n        setMessages((prevMessages) => [\r\n          ...prevMessages,\r\n          data.response as unknown as { content: string; type: \"human\" | \"ai\" },\r\n        ]);\r\n      }\r\n    } else {\r\n      const { data } = await sendMessage({ message });\r\n      if (data) {\r\n        router.push(`/dashboard/chat/${data.chat_id}`);\r\n      }\r\n    }\r\n  };\r\n\r\n  const value = {\r\n    messages,\r\n    isSending,\r\n    isHistoryLoading,\r\n    handleSendMessage,\r\n    chatId,\r\n  };\r\n\r\n  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>;\r\n}\r\n\r\nfunction ChatRoot({ children }: { children: ReactNode }) {\r\n  return (\r\n    <div className=\"flex h-[calc(100vh-3.5rem)] text-foreground\">\r\n      <div className=\"flex flex-col flex-1 glass-card m-4 overflow-hidden\">\r\n        {children}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction ChatMessagesComponent() {\r\n  const { messages, isSending } = useChat();\r\n  return <ChatMessages messages={messages} isLoading={isSending} />;\r\n}\r\n\r\nfunction ChatInputComponent() {\r\n  const { handleSendMessage, isSending } = useChat();\r\n  return <ChatInput onSendMessage={handleSendMessage} isLoading={isSending} />;\r\n}\r\n\r\nfunction ChatLoadingComponent() {\r\n  const { isHistoryLoading } = useChat();\r\n  if (!isHistoryLoading) return null;\r\n  return (\r\n    <div className=\"flex h-[calc(100vh-3.5rem)] text-foreground items-center justify-center\">\r\n      <div className=\"glass-card p-8\">\r\n        <ChatLoading />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction ChatOptionsComponent() {\r\n  const { messages, chatId } = useChat();\r\n  if (messages.length > 0 || chatId) return null;\r\n  return <ChatOptions />;\r\n}\r\n\r\n// Assign the sub-components to the main Chat component\r\nexport const Chat = Object.assign(ChatRoot, {\r\n  Provider: ChatProvider,\r\n  Messages: ChatMessagesComponent,\r\n  Input: ChatInputComponent,\r\n  Loading: ChatLoadingComponent,\r\n  Options: ChatOptionsComponent,\r\n});\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAOA;AACA;AACA;AACA;AACA;AACA;;;AAdA;;;;;;;;AAwBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAYN,SAAS,aAAa,KAA+B;QAA/B,EAAE,QAAQ,EAAqB,GAA/B;;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,KAAK,OAAO,EAAE;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAErC,EAAE;IACJ,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,OAAO,QAAQ,OAAO;IAC1E,MAAM,CAAC,aAAa,EAAE,WAAW,SAAS,EAAE,CAAC,GAAG,CAAA,GAAA,+GAAA,CAAA,yBAAsB,AAAD;IACrE,MAAM,EACJ,MAAM,eAAe,EACrB,OAAO,EACP,WAAW,gBAAgB,EAC5B,GAAG,CAAA,GAAA,+GAAA,CAAA,yBAAsB,AAAD,EAAE,QAAS;QAAE,MAAM,CAAC;IAAO;IAEpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,OAAO,OAAO;gBAChB,UAAU;gBACV,YAAY,EAAE;YAChB,OAAO;gBACL,UAAU;gBACV;YACF;QACF;iCAAG;QAAC;QAAI;KAAQ;IAEhB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,iBAAiB;gBACnB,YACE,gBAAgB,QAAQ;YAK5B;QACF;iCAAG;QAAC;KAAgB;IAEpB,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,QAAQ,IAAI,IAAI;QAErB,MAAM,cAAc;eACf;YACH;gBAAE,SAAS;gBAAS,MAAM;YAAiB;SAC5C;QACD,YAAY;QAEZ,IAAI,QAAQ;YACV,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,YAAY;gBAAE;gBAAS,SAAS;YAAO;YAC9D,IAAI,QAAQ,KAAK,QAAQ,EAAE;gBACzB,YAAY,CAAC,eAAiB;2BACzB;wBACH,KAAK,QAAQ;qBACd;YACH;QACF,OAAO;YACL,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,YAAY;gBAAE;YAAQ;YAC7C,IAAI,MAAM;gBACR,OAAO,IAAI,CAAC,AAAC,mBAA+B,OAAb,KAAK,OAAO;YAC7C;QACF;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;IAtEgB;;QACC,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;QAMwB,+GAAA,CAAA,yBAAsB;QAKlE,+GAAA,CAAA,yBAAsB;;;KAbZ;AAwEhB,SAAS,SAAS,KAAqC;QAArC,EAAE,QAAQ,EAA2B,GAArC;IAChB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;MARS;AAUT,SAAS;;IACP,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG;IAChC,qBAAO,6LAAC,sIAAA,CAAA,UAAY;QAAC,UAAU;QAAU,WAAW;;;;;;AACtD;IAHS;;QACyB;;;MADzB;AAKT,SAAS;;IACP,MAAM,EAAE,iBAAiB,EAAE,SAAS,EAAE,GAAG;IACzC,qBAAO,6LAAC,mIAAA,CAAA,UAAS;QAAC,eAAe;QAAmB,WAAW;;;;;;AACjE;IAHS;;QACkC;;;MADlC;AAKT,SAAS;;IACP,MAAM,EAAE,gBAAgB,EAAE,GAAG;IAC7B,IAAI,CAAC,kBAAkB,OAAO;IAC9B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,qIAAA,CAAA,UAAW;;;;;;;;;;;;;;;AAIpB;IAVS;;QACsB;;;MADtB;AAYT,SAAS;;IACP,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IAC7B,IAAI,SAAS,MAAM,GAAG,KAAK,QAAQ,OAAO;IAC1C,qBAAO,6LAAC,oIAAA,CAAA,UAAW;;;;;AACrB;IAJS;;QACsB;;;MADtB;AAOF,MAAM,OAAO,OAAO,MAAM,CAAC,UAAU;IAC1C,UAAU;IACV,UAAU;IACV,OAAO;IACP,SAAS;IACT,SAAS;AACX", "debugId": null}}, {"offset": {"line": 1161, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/app/dashboard/chat/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Chat } from \"@/components/chat/compound\";\r\n\r\nexport default function ChatPage() {\r\n  return (\r\n    <Chat.Provider>\r\n      <Chat>\r\n        <Chat.Loading />\r\n        <Chat.Options />\r\n        <Chat.Messages />\r\n        <Chat.Input />\r\n      </Chat>\r\n    </Chat.Provider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,6LAAC,2IAAA,CAAA,OAAI,CAAC,QAAQ;kBACZ,cAAA,6LAAC,2IAAA,CAAA,OAAI;;8BACH,6LAAC,2IAAA,CAAA,OAAI,CAAC,OAAO;;;;;8BACb,6LAAC,2IAAA,CAAA,OAAI,CAAC,OAAO;;;;;8BACb,6LAAC,2IAAA,CAAA,OAAI,CAAC,QAAQ;;;;;8BACd,6LAAC,2IAAA,CAAA,OAAI,CAAC,KAAK;;;;;;;;;;;;;;;;AAInB;KAXwB", "debugId": null}}]}