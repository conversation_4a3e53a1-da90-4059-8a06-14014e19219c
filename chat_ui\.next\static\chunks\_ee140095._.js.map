{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/Header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { SidebarTrigger, useSidebar } from \"./ui/sidebar\";\r\nimport { Button } from \"./ui/button\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { IoMdAdd } from \"react-icons/io\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { ThemeToggle } from \"./theme-toggle\";\r\n\r\nexport default function Header() {\r\n  const router = useRouter();\r\n  const { state } = useSidebar();\r\n\r\n  const handleNewChat = () => {\r\n    router.push(\"/dashboard/chat/new\");\r\n  };\r\n\r\n  return (\r\n    <header\r\n      className={cn([\r\n        \"bg-gray-800 py-2 px-4 flex items-center justify-between border-b border-gray-700 text-white fixed  z-50 h-14 transition-all duration-200 ease-linear w-full \",\r\n        state === \"expanded\"\r\n          ? \"md:w-[calc(100vw-248px)]\"\r\n          : \"md:w-[calc(100vw-40px)]\",\r\n      ])}\r\n    >\r\n      <SidebarTrigger />\r\n      <Button\r\n        variant=\"outline\"\r\n        onClick={handleNewChat}\r\n        className=\"flex items-center gap-2 text-black\"\r\n      >\r\n        <IoMdAdd className=\"h-5 w-5\" />\r\n        New Chat\r\n      </Button>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AASe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,aAAU,AAAD;IAE3B,MAAM,gBAAgB;QACpB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE;YACZ;YACA,UAAU,aACN,6BACA;SACL;;0BAED,6LAAC,+HAAA,CAAA,iBAAc;;;;;0BACf,6LAAC,8HAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,SAAS;gBACT,WAAU;;kCAEV,6LAAC,iJAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;oBAAY;;;;;;;;;;;;;AAKvC;GA5BwB;;QACP,qIAAA,CAAA,YAAS;QACN,+HAAA,CAAA,aAAU;;;KAFN", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/app/dashboard/layout.tsx"], "sourcesContent": ["\"use client\"\r\nimport Header from \"@/components/Header\";\r\n\r\nexport default function DashboardLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  return (\r\n    <div className=\"relative \">\r\n      <Header />\r\n      <div className=\"pt-14 \">\r\n        {children}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAGe,SAAS,gBAAgB,KAItC;QAJsC,EACtC,QAAQ,EAGR,GAJsC;IAKtC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,wHAAA,CAAA,UAAM;;;;;0BACP,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;KAbwB", "debugId": null}}]}