{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/app/page.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport Link from \"next/link\";\nimport { ThemeToggle } from \"@/components/theme-toggle\";\n\nexport const metadata: Metadata = {\n  title: \"Personal Assistance\",\n  description: \"Welcome to Personal Assistance!\",\n};\n\nexport default function Index() {\n  return (\n    <main className=\"min-h-screen flex flex-col items-center justify-center text-foreground px-4 relative\">\n      <div className=\"relative z-10 flex flex-col items-center\">\n        <div className=\"glass-card p-8 mb-8 rounded-full\">\n          <img\n            src=\"/robot.png\"\n            alt=\"Personal Assistant Robot\"\n            className=\"w-32 h-32 drop-shadow-[0_0_2rem_rgba(99,102,241,0.5)] animate-fade-in rounded-full\"\n            style={{ objectFit: 'cover' }}\n          />\n        </div>\n        <div className=\"glass-card p-8 text-center max-w-2xl\">\n          <h1 className=\"text-5xl font-bold mb-4 tracking-tighter bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600\">\n            Personal Assistance\n          </h1>\n          <p className=\"text-lg text-muted-foreground mb-8 max-w-md mx-auto\">\n            Your AI-powered assistant for productivity, organization, and creativity.<br />\n            <span className=\"text-primary font-medium\">Minimal. Fast. Reliable.</span>\n          </p>\n          <div className=\"flex flex-col gap-4 w-full max-w-xs mx-auto\">\n            <Link\n              href=\"/dashboard/chat/new\"\n              className=\"glass-button bg-primary/80 text-primary-foreground rounded-lg py-3 px-6 font-semibold transition-all duration-300 ease-in-out text-center transform hover:scale-105 border-0\"\n            >\n              Try a Demo Chat\n            </Link>\n            <a\n              href=\"https://github.com/your-repo\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-sm text-muted-foreground underline text-center hover:text-primary transition-colors\"\n            >\n              Learn more on GitHub\n            </a>\n          </div>\n        </div>\n        <footer className=\"mt-8 text-xs text-muted-foreground opacity-70\">\n          &copy; {new Date().getFullYear()} Personal Assistance. All rights reserved.\n        </footer>\n      </div>\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAK,WAAU;kBACd,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,KAAI;wBACJ,KAAI;wBACJ,WAAU;wBACV,OAAO;4BAAE,WAAW;wBAAQ;;;;;;;;;;;8BAGhC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAmH;;;;;;sCAGjI,8OAAC;4BAAE,WAAU;;gCAAsD;8CACQ,8OAAC;;;;;8CAC1E,8OAAC;oCAAK,WAAU;8CAA2B;;;;;;;;;;;;sCAE7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;8CACX;;;;;;;;;;;;;;;;;;8BAKL,8OAAC;oBAAO,WAAU;;wBAAgD;wBACxD,IAAI,OAAO,WAAW;wBAAG;;;;;;;;;;;;;;;;;;AAK3C", "debugId": null}}]}