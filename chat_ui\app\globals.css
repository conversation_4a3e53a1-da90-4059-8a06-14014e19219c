@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  /* Glass theme variables */
  --glass-bg: var(--glass-background);
  --glass-border: var(--glass-border-color);
  --glass-shadow: var(--glass-shadow-color);
  --glass-backdrop: var(--glass-backdrop-filter);
}

:root {
  --radius: 0.625rem;

  /* Light mode glass theme */
  --background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  --foreground: oklch(0.145 0 0);
  --card: rgba(255, 255, 255, 0.1);
  --card-foreground: oklch(0.145 0 0);
  --popover: rgba(255, 255, 255, 0.15);
  --popover-foreground: oklch(0.145 0 0);
  --primary: rgba(59, 130, 246, 0.8);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: rgba(255, 255, 255, 0.08);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: rgba(255, 255, 255, 0.05);
  --muted-foreground: oklch(0.556 0 0);
  --accent: rgba(255, 255, 255, 0.1);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: rgba(255, 255, 255, 0.2);
  --input: rgba(255, 255, 255, 0.1);
  --ring: rgba(59, 130, 246, 0.5);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: rgba(255, 255, 255, 0.08);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: rgba(59, 130, 246, 0.8);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: rgba(255, 255, 255, 0.1);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: rgba(255, 255, 255, 0.2);
  --sidebar-ring: rgba(59, 130, 246, 0.5);

  /* Glass effect variables */
  --glass-background: rgba(255, 255, 255, 0.1);
  --glass-border-color: rgba(255, 255, 255, 0.2);
  --glass-shadow-color: rgba(0, 0, 0, 0.1);
  --glass-backdrop-filter: blur(20px) saturate(180%);
}

.dark {
  /* Dark mode glass theme */
  --background: linear-gradient(135deg, rgba(15, 23, 42, 0.8), rgba(30, 41, 59, 0.6));
  --foreground: oklch(0.985 0 0);
  --card: rgba(30, 41, 59, 0.3);
  --card-foreground: oklch(0.985 0 0);
  --popover: rgba(30, 41, 59, 0.4);
  --popover-foreground: oklch(0.985 0 0);
  --primary: rgba(99, 102, 241, 0.8);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: rgba(30, 41, 59, 0.2);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: rgba(30, 41, 59, 0.15);
  --muted-foreground: oklch(0.708 0 0);
  --accent: rgba(30, 41, 59, 0.25);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: rgba(255, 255, 255, 0.1);
  --input: rgba(255, 255, 255, 0.05);
  --ring: rgba(99, 102, 241, 0.5);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: rgba(15, 23, 42, 0.4);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: rgba(99, 102, 241, 0.8);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: rgba(30, 41, 59, 0.25);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: rgba(255, 255, 255, 0.1);
  --sidebar-ring: rgba(99, 102, 241, 0.5);

  /* Dark mode glass effect variables */
  --glass-background: rgba(15, 23, 42, 0.3);
  --glass-border-color: rgba(255, 255, 255, 0.1);
  --glass-shadow-color: rgba(0, 0, 0, 0.3);
  --glass-backdrop-filter: blur(20px) saturate(180%);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply text-foreground;
    background: var(--background);
    min-height: 100vh;
    position: relative;
  }

  /* Glass background pattern */
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
      radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    z-index: -2;
    pointer-events: none;
  }

  /* Glass noise texture */
  body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
      radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0);
    background-size: 20px 20px;
    z-index: -1;
    pointer-events: none;
    opacity: 0.5;
  }

  .dark body::before {
    background:
      radial-gradient(circle at 20% 50%, rgba(99, 102, 241, 0.4) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.4) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(14, 165, 233, 0.4) 0%, transparent 50%);
  }

  .dark body::after {
    background-image:
      radial-gradient(circle at 1px 1px, rgba(255,255,255,0.05) 1px, transparent 0);
  }
}

/* Glass morphism utility classes */
@layer utilities {
  .glass-bg {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    box-shadow:
      0 8px 32px var(--glass-shadow),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .glass-card {
    @apply glass-bg rounded-xl;
    background: var(--glass-bg);
  }

  .glass-header {
    @apply glass-bg;
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid var(--glass-border);
  }

  .glass-sidebar {
    @apply glass-bg;
    background: var(--glass-bg);
    border-right: 1px solid var(--glass-border);
  }

  .glass-input {
    @apply glass-bg rounded-lg;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid var(--glass-border);
  }

  .glass-button {
    @apply glass-bg rounded-lg;
    background: var(--glass-bg);
    transition: all 0.3s ease;
  }



  .glass-button:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
    box-shadow:
      0 12px 40px var(--glass-shadow),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .dark .glass-header {
    background: rgba(15, 23, 42, 0.2);
  }

  .dark .glass-input {
    background: rgba(15, 23, 42, 0.3);
  }

  .dark .glass-button:hover {
    background: rgba(255, 255, 255, 0.08);
  }

  /* Enhanced glass effects */
  .glass-intense {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(30px) saturate(200%);
    -webkit-backdrop-filter: blur(30px) saturate(200%);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.2),
      inset 0 -1px 0 rgba(0, 0, 0, 0.1);
  }

  .dark .glass-intense {
    background: rgba(15, 23, 42, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.2);
  }

  /* Animated glass shimmer effect */
  .glass-shimmer {
    position: relative;
    overflow: hidden;
  }

  .glass-shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    animation: shimmer 3s infinite;
  }

  @keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }

  /* Floating animation for glass elements */
  .glass-float {
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }
}
