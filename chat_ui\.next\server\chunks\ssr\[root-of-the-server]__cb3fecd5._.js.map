{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/CommandBadge.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\ninterface CommandBadgeProps {\r\n  command: \"generate image\" | \"scrape website\";\r\n}\r\n\r\nconst commandStyles = {\r\n  \"generate image\": {\r\n    label: \"Image\",\r\n    bgColor: \"bg-blue-500/80\",\r\n  },\r\n  \"scrape website\": {\r\n    label: \"Scrape\",\r\n    bgColor: \"bg-green-500/80\",\r\n  },\r\n};\r\n\r\nexport default function CommandBadge({ command }: CommandBadgeProps) {\r\n  const { label, bgColor } = commandStyles[command];\r\n\r\n  return (\r\n    <span\r\n      className={`glass-card inline-flex items-center px-2.5 py-0.5 rounded-sm text-xs font-medium text-white border-0 ${bgColor}`}\r\n    >\r\n      {label}\r\n    </span>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAMA,MAAM,gBAAgB;IACpB,kBAAkB;QAChB,OAAO;QACP,SAAS;IACX;IACA,kBAAkB;QAChB,OAAO;QACP,SAAS;IACX;AACF;AAEe,SAAS,aAAa,EAAE,OAAO,EAAqB;IACjE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,aAAa,CAAC,QAAQ;IAEjD,qBACE,8OAAC;QACC,WAAW,CAAC,qGAAqG,EAAE,SAAS;kBAE3H;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/ChatInput.tsx"], "sourcesContent": ["import { useState, useRef, useEffect } from \"react\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { FaPaperPlane } from \"react-icons/fa\";\r\nimport CommandBadge from \"./CommandBadge\";\r\n\r\ninterface ChatInputProps {\r\n  onSendMessage: (message: string) => void;\r\n  isLoading: boolean;\r\n}\r\n\r\ntype Command = \"generate image\" | \"scrape website\";\r\n\r\nexport default function ChatInput({ onSendMessage, isLoading }: ChatInputProps) {\r\n  const [input, setInput] = useState(\"\");\r\n  const [showMenu, setShowMenu] = useState(false);\r\n  const [activeCommand, setActiveCommand] = useState<Command | null>(null);\r\n  const [focusedIndex, setFocusedIndex] = useState(0);\r\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\r\n  const badgeRef = useRef<HTMLSpanElement>(null);\r\n  const commands: Command[] = [\"generate image\", \"scrape website\"];\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\r\n    const value = e.target.value;\r\n    setInput(value);\r\n\r\n    if (activeCommand) {\r\n      return;\r\n    }\r\n\r\n    if (value === \"/\") {\r\n      setShowMenu(true);\r\n      setFocusedIndex(0);\r\n    } else {\r\n      setShowMenu(false);\r\n    }\r\n  };\r\n\r\n  const handleSendMessage = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!input.trim() && !activeCommand) return;\r\n\r\n    const message = activeCommand ? `/${activeCommand} ${input}` : input;\r\n    onSendMessage(message);\r\n    setInput(\"\");\r\n    setActiveCommand(null);\r\n    setShowMenu(false);\r\n  };\r\n\r\n  const handleOptionClick = (option: Command) => {\r\n    setActiveCommand(option);\r\n    setInput(\"\");\r\n    setShowMenu(false);\r\n    setTimeout(() => {\r\n      textareaRef.current?.focus();\r\n    }, 0);\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (textareaRef.current) {\r\n      textareaRef.current.style.height = \"auto\";\r\n      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;\r\n    }\r\n  }, [input, activeCommand]);\r\n\r\n  useEffect(() => {\r\n    if (textareaRef.current && badgeRef.current) {\r\n      const badgeWidth = badgeRef.current.offsetWidth;\r\n      textareaRef.current.style.textIndent = `${badgeWidth + 8}px`;\r\n    } else if (textareaRef.current) {\r\n      textareaRef.current.style.textIndent = \"0px\";\r\n    }\r\n  }, [activeCommand]);\r\n\r\n  return (\r\n    <footer className=\"glass-header py-4 px-6 border-t-0 border-b-0\">\r\n      <div className=\"relative\">\r\n        {showMenu && (\r\n          <div className=\"absolute bottom-full mb-2 w-full glass-card rounded-lg shadow-2xl z-10\">\r\n            <ul>\r\n              {commands.map((command, index) => (\r\n                <li\r\n                  key={command}\r\n                  className={`px-4 py-2 cursor-pointer rounded-lg transition-all duration-200 ${\r\n                    index === focusedIndex\r\n                      ? \"bg-primary/20 text-primary\"\r\n                      : \"text-foreground hover:bg-white/10\"\r\n                  }`}\r\n                  onClick={() => handleOptionClick(command)}\r\n                  onMouseEnter={() => setFocusedIndex(index)}\r\n                >\r\n                  {command.charAt(0).toUpperCase() + command.slice(1)}\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n        )}\r\n        <form onSubmit={handleSendMessage} className=\"relative\">\r\n          <div className=\"relative flex items-center\">\r\n            {activeCommand && (\r\n              <div\r\n                ref={badgeRef as any}\r\n                className=\"absolute top-1.5 left-3\"\r\n              >\r\n                <CommandBadge command={activeCommand} />\r\n              </div>\r\n            )}\r\n            <Textarea\r\n              ref={textareaRef}\r\n              placeholder={\r\n                activeCommand ? \"\" : \"Type your message or / for commands\"\r\n              }\r\n              className=\"glass-input pr-16 rounded-lg text-foreground placeholder:text-muted-foreground focus:ring-primary/50 focus:border-primary/50 w-full border-0\"\r\n              value={input}\r\n              onChange={handleInputChange}\r\n              onKeyDown={(e) => {\r\n                if (showMenu) {\r\n                  if (e.key === \"ArrowUp\") {\r\n                    e.preventDefault();\r\n                    setFocusedIndex((prevIndex) =>\r\n                      prevIndex > 0 ? prevIndex - 1 : commands.length - 1\r\n                    );\r\n                  } else if (e.key === \"ArrowDown\") {\r\n                    e.preventDefault();\r\n                    setFocusedIndex((prevIndex) =>\r\n                      prevIndex < commands.length - 1 ? prevIndex + 1 : 0\r\n                    );\r\n                  } else if (e.key === \"Enter\") {\r\n                    e.preventDefault();\r\n                    handleOptionClick(commands[focusedIndex]);\r\n                  }\r\n                } else if (e.key === \"Enter\" && !e.shiftKey) {\r\n                  handleSendMessage(e);\r\n                }\r\n\r\n                if (e.key === \"Backspace\" && input === \"\" && activeCommand) {\r\n                  setActiveCommand(null);\r\n                }\r\n              }}\r\n              rows={1}\r\n            />\r\n            <Button\r\n              type=\"submit\"\r\n              className=\"glass-button absolute top-1/2 right-4 -translate-y-1/2 bg-primary/80 hover:bg-primary text-primary-foreground border-0\"\r\n              disabled={isLoading}\r\n            >\r\n              <FaPaperPlane className=\"w-5 h-5\" />\r\n            </Button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n    </footer>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AASe,SAAS,UAAU,EAAE,aAAa,EAAE,SAAS,EAAkB;IAC5E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAuB;IAChD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAmB;IACzC,MAAM,WAAsB;QAAC;QAAkB;KAAiB;IAEhE,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,SAAS;QAET,IAAI,eAAe;YACjB;QACF;QAEA,IAAI,UAAU,KAAK;YACjB,YAAY;YACZ,gBAAgB;QAClB,OAAO;YACL,YAAY;QACd;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,eAAe;QAErC,MAAM,UAAU,gBAAgB,CAAC,CAAC,EAAE,cAAc,CAAC,EAAE,OAAO,GAAG;QAC/D,cAAc;QACd,SAAS;QACT,iBAAiB;QACjB,YAAY;IACd;IAEA,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;QACjB,SAAS;QACT,YAAY;QACZ,WAAW;YACT,YAAY,OAAO,EAAE;QACvB,GAAG;IACL;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,OAAO,EAAE;YACvB,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;YACnC,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,YAAY,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;QAC5E;IACF,GAAG;QAAC;QAAO;KAAc;IAEzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,OAAO,IAAI,SAAS,OAAO,EAAE;YAC3C,MAAM,aAAa,SAAS,OAAO,CAAC,WAAW;YAC/C,YAAY,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,aAAa,EAAE,EAAE,CAAC;QAC9D,OAAO,IAAI,YAAY,OAAO,EAAE;YAC9B,YAAY,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG;QACzC;IACF,GAAG;QAAC;KAAc;IAElB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;gBACZ,0BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCACE,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;gCAEC,WAAW,CAAC,gEAAgE,EAC1E,UAAU,eACN,+BACA,qCACJ;gCACF,SAAS,IAAM,kBAAkB;gCACjC,cAAc,IAAM,gBAAgB;0CAEnC,QAAQ,MAAM,CAAC,GAAG,WAAW,KAAK,QAAQ,KAAK,CAAC;+BAT5C;;;;;;;;;;;;;;;8BAef,8OAAC;oBAAK,UAAU;oBAAmB,WAAU;8BAC3C,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,+BACC,8OAAC;gCACC,KAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC,mIAAA,CAAA,UAAY;oCAAC,SAAS;;;;;;;;;;;0CAG3B,8OAAC,6HAAA,CAAA,WAAQ;gCACP,KAAK;gCACL,aACE,gBAAgB,KAAK;gCAEvB,WAAU;gCACV,OAAO;gCACP,UAAU;gCACV,WAAW,CAAC;oCACV,IAAI,UAAU;wCACZ,IAAI,EAAE,GAAG,KAAK,WAAW;4CACvB,EAAE,cAAc;4CAChB,gBAAgB,CAAC,YACf,YAAY,IAAI,YAAY,IAAI,SAAS,MAAM,GAAG;wCAEtD,OAAO,IAAI,EAAE,GAAG,KAAK,aAAa;4CAChC,EAAE,cAAc;4CAChB,gBAAgB,CAAC,YACf,YAAY,SAAS,MAAM,GAAG,IAAI,YAAY,IAAI;wCAEtD,OAAO,IAAI,EAAE,GAAG,KAAK,SAAS;4CAC5B,EAAE,cAAc;4CAChB,kBAAkB,QAAQ,CAAC,aAAa;wCAC1C;oCACF,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;wCAC3C,kBAAkB;oCACpB;oCAEA,IAAI,EAAE,GAAG,KAAK,eAAe,UAAU,MAAM,eAAe;wCAC1D,iBAAiB;oCACnB;gCACF;gCACA,MAAM;;;;;;0CAER,8OAAC,2HAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;gCACV,UAAU;0CAEV,cAAA,8OAAC,8IAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC", "debugId": null}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/ChatMessage.tsx"], "sourcesContent": ["import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>Cir<PERSON> } from \"react-icons/fa\";\r\nimport { Download, Copy, Check } from \"lucide-react\";\r\nimport React, { useState } from \"react\";\r\nimport ReactMarkdown from \"react-markdown\";\r\nimport { downloadImage } from \"@/lib/utils\";\r\nimport { Prism as Syntax<PERSON>ighlighter } from \"react-syntax-highlighter\";\r\nimport { vscDarkPlus } from \"react-syntax-highlighter/dist/esm/styles/prism\";\r\nimport remarkGfm from \"remark-gfm\";\r\n\r\ninterface ChatMessageProps {\r\n  message: {\r\n    content: string;\r\n    type: \"human\" | \"ai\";\r\n    image_url?: string;\r\n    status?: \"processing\" | \"complete\";\r\n  };\r\n}\r\n\r\nexport default function ChatMessage({ message }: ChatMessageProps) {\r\n  const isHuman = message.type === \"human\";\r\n  const [copied, setCopied] = useState(false);\r\n\r\n  const handleCopy = (text: string) => {\r\n    navigator.clipboard.writeText(text);\r\n    setCopied(true);\r\n    setTimeout(() => setCopied(false), 2000);\r\n  };\r\n  return (\r\n    <div className={`flex items-start gap-4 ${isHuman ? \"justify-end\" : \"\"}`}>\r\n      {!isHuman && (\r\n        <div className=\"rounded-full glass-card w-8 h-8 flex items-center justify-center\">\r\n          <FaRobot className=\"w-5 h-5 text-primary\" />\r\n        </div>\r\n      )}\r\n      <div\r\n        className={`${\r\n          isHuman\r\n            ? \"glass-card bg-primary/20 border-primary/30\"\r\n            : \"glass-card\"\r\n        } rounded-lg p-4 max-w-[75%] relative group`}\r\n      >\r\n        {message.status && (\r\n          <div className=\"absolute top-2 right-10 text-foreground\">\r\n            {message.status === \"processing\" && (\r\n              <FaSpinner className=\"animate-spin h-5 w-5 text-primary\" />\r\n            )}\r\n            {message.status === \"complete\" && (\r\n              <FaCheckCircle className=\"h-5 w-5 text-green-500\" />\r\n            )}\r\n          </div>\r\n        )}\r\n        <button\r\n          onClick={() => handleCopy(message.content)}\r\n          className=\"glass-button absolute top-2 right-2 text-foreground p-2 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-200 border-0\"\r\n        >\r\n          {copied ? (\r\n            <Check className=\"h-5 w-5 text-green-500\" />\r\n          ) : (\r\n            <Copy className=\"h-5 w-5\" />\r\n          )}\r\n        </button>\r\n        <ReactMarkdown\r\n          remarkPlugins={[remarkGfm]}\r\n          components={{\r\n            code({ node, inline, className, children, ...props }: any) {\r\n              const match = /language-(\\w+)/.exec(className || \"\");\r\n              return !inline && match ? (\r\n                <div className=\"relative\">\r\n                  <button\r\n                    onClick={() => handleCopy(String(children))}\r\n                    className=\"glass-button absolute top-2 right-2 text-foreground p-2 rounded-full border-0\"\r\n                  >\r\n                    {copied ? (\r\n                      <Check className=\"h-5 w-5 text-green-500\" />\r\n                    ) : (\r\n                      <Copy className=\"h-5 w-5\" />\r\n                    )}\r\n                  </button>\r\n                  <SyntaxHighlighter\r\n                    style={vscDarkPlus as any}\r\n                    language={match[1]}\r\n                    PreTag=\"div\"\r\n                    {...props}\r\n                  >\r\n                    {String(children).replace(/\\n$/, \"\")}\r\n                  </SyntaxHighlighter>\r\n                </div>\r\n              ) : (\r\n                <code className={className} {...props}>\r\n                  {children}\r\n                </code>\r\n              );\r\n            },\r\n          }}\r\n        >\r\n          {message.content}\r\n        </ReactMarkdown>\r\n        {message.image_url && (\r\n          <div className=\"mt-2 relative group\">\r\n            <img\r\n              src={message.image_url}\r\n              alt=\"Generated image\"\r\n              className=\"rounded-lg max-w-full h-auto\"\r\n            />\r\n            <button\r\n              onClick={() => downloadImage(message.image_url!)}\r\n              className=\"glass-button absolute top-2 right-2 text-foreground p-2 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-200 border-0\"\r\n            >\r\n              <Download className=\"h-5 w-5\" />\r\n            </button>\r\n          </div>\r\n        )}\r\n      </div>\r\n      {isHuman && (\r\n        <div className=\"rounded-full glass-card w-8 h-8 flex items-center justify-center\">\r\n          <FaUser className=\"w-5 h-5 text-primary\" />\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAWe,SAAS,YAAY,EAAE,OAAO,EAAoB;IAC/D,MAAM,UAAU,QAAQ,IAAI,KAAK;IACjC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,aAAa,CAAC;QAClB,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,UAAU;QACV,WAAW,IAAM,UAAU,QAAQ;IACrC;IACA,qBACE,8OAAC;QAAI,WAAW,CAAC,uBAAuB,EAAE,UAAU,gBAAgB,IAAI;;YACrE,CAAC,yBACA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,8IAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;;0BAGvB,8OAAC;gBACC,WAAW,GACT,UACI,+CACA,aACL,0CAA0C,CAAC;;oBAE3C,QAAQ,MAAM,kBACb,8OAAC;wBAAI,WAAU;;4BACZ,QAAQ,MAAM,KAAK,8BAClB,8OAAC,8IAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAEtB,QAAQ,MAAM,KAAK,4BAClB,8OAAC,8IAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;;kCAI/B,8OAAC;wBACC,SAAS,IAAM,WAAW,QAAQ,OAAO;wBACzC,WAAU;kCAET,uBACC,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;iDAEjB,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAGpB,8OAAC,wLAAA,CAAA,UAAa;wBACZ,eAAe;4BAAC,6IAAA,CAAA,UAAS;yBAAC;wBAC1B,YAAY;4BACV,MAAK,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAY;gCACvD,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;gCACjD,OAAO,CAAC,UAAU,sBAChB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,WAAW,OAAO;4CACjC,WAAU;sDAET,uBACC,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;uEAEjB,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAGpB,8OAAC,0MAAA,CAAA,QAAiB;4CAChB,OAAO,iPAAA,CAAA,cAAW;4CAClB,UAAU,KAAK,CAAC,EAAE;4CAClB,QAAO;4CACN,GAAG,KAAK;sDAER,OAAO,UAAU,OAAO,CAAC,OAAO;;;;;;;;;;;2DAIrC,8OAAC;oCAAK,WAAW;oCAAY,GAAG,KAAK;8CAClC;;;;;;4BAGP;wBACF;kCAEC,QAAQ,OAAO;;;;;;oBAEjB,QAAQ,SAAS,kBAChB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,KAAK,QAAQ,SAAS;gCACtB,KAAI;gCACJ,WAAU;;;;;;0CAEZ,8OAAC;gCACC,SAAS,IAAM,CAAA,GAAA,4GAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,SAAS;gCAC9C,WAAU;0CAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAK3B,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,8IAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAK5B", "debugId": null}}, {"offset": {"line": 543, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/ChatMessages.tsx"], "sourcesContent": ["import { useEffect, useRef } from \"react\";\r\nimport { FaRobot } from \"react-icons/fa\";\r\nimport ChatMessage from \"./ChatMessage\";\r\nimport ChatLoading from \"./ChatLoading\";\r\n\r\ninterface Message {\r\n  content: string;\r\n  type: \"human\" | \"ai\";\r\n  status?: \"processing\" | \"complete\";\r\n}\r\n\r\ninterface ChatMessagesProps {\r\n  messages: Message[];\r\n  isLoading?: boolean;\r\n}\r\n\r\nexport default function ChatMessages({ messages, isLoading }: ChatMessagesProps) {\r\n  const messagesEndRef = useRef<HTMLDivElement | null>(null);\r\n\r\n  const scrollToBottom = () => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages, isLoading]);\r\n\r\n  return (\r\n    <main className=\"flex-1 overflow-y-auto p-6 bg-transparent\">\r\n      <div className=\"space-y-4\">\r\n        {messages.map((message, index) => (\r\n          <ChatMessage key={index} message={message} />\r\n        ))}\r\n        {isLoading && (\r\n          <div className=\"flex items-start gap-4\">\r\n            <div className=\"rounded-full glass-card w-8 h-8 flex items-center justify-center\">\r\n              <FaRobot className=\"w-5 h-5 text-muted-foreground\" />\r\n            </div>\r\n            <div className=\"glass-card rounded-lg p-4 max-w-[75%] flex items-center gap-2\">\r\n              <ChatLoading className=\"h-5 w-5\" />\r\n              <span className=\"text-muted-foreground\">thinking...</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n        <div ref={messagesEndRef} />\r\n      </div>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAae,SAAS,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAqB;IAC7E,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAErD,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAU;KAAU;IAExB,qBACE,8OAAC;QAAK,WAAU;kBACd,cAAA,8OAAC;YAAI,WAAU;;gBACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,kIAAA,CAAA,UAAW;wBAAa,SAAS;uBAAhB;;;;;gBAEnB,2BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,8IAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAErB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,UAAW;oCAAC,WAAU;;;;;;8CACvB,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAI9C,8OAAC;oBAAI,KAAK;;;;;;;;;;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 651, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 746, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/ChatOption.tsx"], "sourcesContent": ["import { Card } from \"../ui/card\";\r\nimport { FaScrewdriverWrench } from \"react-icons/fa6\";\r\nimport { LuImagePlus } from \"react-icons/lu\";\r\nimport { FaSignsPost } from \"react-icons/fa6\";\r\nimport Link from \"next/link\";\r\n\r\nconst data = [\r\n  {\r\n    title: \"Scrape Website\",\r\n    description: \"Scrape website data and extract information.\",\r\n    icon: FaScrewdriverWrench,\r\n    param: \"scrape_website\",\r\n  },\r\n  {\r\n    title: \"Generate Image\",\r\n    description: \"Generate Image from text.\",\r\n    icon: LuImagePlus,\r\n    param: \"generate_image\",\r\n  },\r\n  {\r\n    title: \"Generate Poster Images\",\r\n    description: \"Generate poster images from text.\",\r\n    icon: FaSignsPost,\r\n    param: \"generate_poster_images\",\r\n  },\r\n];\r\n\r\nexport default function ChatOptions() {\r\n  return (\r\n    <div className=\"flex items-center justify-center flex-wrap h-full gap-4 p-6\">\r\n      {data.map((item, index) => (\r\n        <Link href={`/dashboard/chat/new?${item.param}`} passHref key={index}>\r\n          <Card className=\"glass-card p-6 text-foreground gap-3 hover:scale-105 transition-all duration-300 cursor-pointer group border-0\">\r\n            <div className=\"flex flex-col items-center text-center space-y-3\">\r\n              <div className=\"glass-card p-3 rounded-full\">\r\n                <item.icon className=\"h-6 w-6 text-primary\" />\r\n              </div>\r\n              <div>\r\n                <p className=\"text-sm font-medium\">{item.title}</p>\r\n                <p className=\"text-xs text-muted-foreground mt-1\">{item.description}</p>\r\n              </div>\r\n            </div>\r\n          </Card>\r\n        </Link>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;;;;;;;AAEA,MAAM,OAAO;IACX;QACE,OAAO;QACP,aAAa;QACb,MAAM,+IAAA,CAAA,sBAAmB;QACzB,OAAO;IACT;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,8IAAA,CAAA,cAAW;QACjB,OAAO;IACT;IACA;QACE,OAAO;QACP,aAAa;QACb,MAAM,+IAAA,CAAA,cAAW;QACjB,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACZ,KAAK,GAAG,CAAC,CAAC,MAAM,sBACf,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAM,CAAC,oBAAoB,EAAE,KAAK,KAAK,EAAE;gBAAE,QAAQ;0BACvD,cAAA,8OAAC,yHAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;;;;;;0CAEvB,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAAuB,KAAK,KAAK;;;;;;kDAC9C,8OAAC;wCAAE,WAAU;kDAAsC,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;eARZ;;;;;;;;;;AAgBvE", "debugId": null}}, {"offset": {"line": 855, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/chat/compound/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  createContext,\r\n  useContext,\r\n  useState,\r\n  useEffect,\r\n  ReactNode,\r\n} from \"react\";\r\nimport { useParams, useRouter } from \"next/navigation\";\r\nimport { useSendMessageMutation, useGetChatHistoryQuery } from \"@/store/api\";\r\nimport ChatInput from \"@/components/chat/ChatInput\";\r\nimport ChatMessages from \"@/components/chat/ChatMessages\";\r\nimport ChatLoading from \"@/components/chat/ChatLoading\";\r\nimport ChatOptions from \"@/components/chat/ChatOption\";\r\n\r\ninterface ChatContextType {\r\n  messages: Array<{ content: string; type: \"human\" | \"ai\" }>;\r\n  isSending: boolean;\r\n  isHistoryLoading: boolean;\r\n  handleSendMessage: (message: string) => void;\r\n  chatId: string | null;\r\n}\r\n\r\nconst ChatContext = createContext<ChatContextType | undefined>(undefined);\r\n\r\nexport const useChat = () => {\r\n  const context = useContext(ChatContext);\r\n  if (!context) {\r\n    throw new Error(\"useChat must be used within a ChatProvider\");\r\n  }\r\n  return context;\r\n};\r\n\r\ninterface ChatProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function ChatProvider({ children }: ChatProviderProps) {\r\n  const params = useParams();\r\n  const router = useRouter();\r\n  const id = params.id as string;\r\n  const [messages, setMessages] = useState<\r\n    Array<{ content: string; type: \"human\" | \"ai\" }>\r\n  >([]);\r\n  const [chatId, setChatId] = useState<string | null>(id === \"new\" ? null : id);\r\n  const [sendMessage, { isLoading: isSending }] = useSendMessageMutation();\r\n  const {\r\n    data: chatHistoryData,\r\n    refetch,\r\n    isLoading: isHistoryLoading,\r\n  } = useGetChatHistoryQuery(chatId!, { skip: !chatId });\r\n\r\n  useEffect(() => {\r\n    if (id === \"new\") {\r\n      setChatId(null);\r\n      setMessages([]);\r\n    } else {\r\n      setChatId(id);\r\n      refetch();\r\n    }\r\n  }, [id, refetch]);\r\n\r\n  useEffect(() => {\r\n    if (chatHistoryData) {\r\n      setMessages(\r\n        chatHistoryData.messages as Array<{\r\n          content: string;\r\n          type: \"human\" | \"ai\";\r\n        }>\r\n      );\r\n    }\r\n  }, [chatHistoryData]);\r\n\r\n  const handleSendMessage = async (message: string) => {\r\n    if (!message.trim()) return;\r\n\r\n    const newMessages = [\r\n      ...messages,\r\n      { content: message, type: \"human\" as const },\r\n    ];\r\n    setMessages(newMessages);\r\n\r\n    if (chatId) {\r\n      const { data } = await sendMessage({ message, chat_id: chatId });\r\n      if (data && data.response) {\r\n        setMessages((prevMessages) => [\r\n          ...prevMessages,\r\n          data.response as unknown as { content: string; type: \"human\" | \"ai\" },\r\n        ]);\r\n      }\r\n    } else {\r\n      const { data } = await sendMessage({ message });\r\n      if (data) {\r\n        router.push(`/dashboard/chat/${data.chat_id}`);\r\n      }\r\n    }\r\n  };\r\n\r\n  const value = {\r\n    messages,\r\n    isSending,\r\n    isHistoryLoading,\r\n    handleSendMessage,\r\n    chatId,\r\n  };\r\n\r\n  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>;\r\n}\r\n\r\nfunction ChatRoot({ children }: { children: ReactNode }) {\r\n  return (\r\n    <div className=\"flex h-[calc(100vh-3.5rem)] text-foreground\">\r\n      <div className=\"flex flex-col flex-1 glass-card m-4 overflow-hidden\">\r\n        {children}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction ChatMessagesComponent() {\r\n  const { messages, isSending } = useChat();\r\n  return <ChatMessages messages={messages} isLoading={isSending} />;\r\n}\r\n\r\nfunction ChatInputComponent() {\r\n  const { handleSendMessage, isSending } = useChat();\r\n  return <ChatInput onSendMessage={handleSendMessage} isLoading={isSending} />;\r\n}\r\n\r\nfunction ChatLoadingComponent() {\r\n  const { isHistoryLoading } = useChat();\r\n  if (!isHistoryLoading) return null;\r\n  return (\r\n    <div className=\"flex h-[calc(100vh-3.5rem)] text-foreground items-center justify-center\">\r\n      <div className=\"glass-card p-8\">\r\n        <ChatLoading />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction ChatOptionsComponent() {\r\n  const { messages, chatId } = useChat();\r\n  if (messages.length > 0 || chatId) return null;\r\n  return <ChatOptions />;\r\n}\r\n\r\n// Assign the sub-components to the main Chat component\r\nexport const Chat = Object.assign(ChatRoot, {\r\n  Provider: ChatProvider,\r\n  Messages: ChatMessagesComponent,\r\n  Input: ChatInputComponent,\r\n  Loading: ChatLoadingComponent,\r\n  Options: ChatOptionsComponent,\r\n});\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAOA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;AAwBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,KAAK,OAAO,EAAE;IACpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAErC,EAAE;IACJ,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,OAAO,QAAQ,OAAO;IAC1E,MAAM,CAAC,aAAa,EAAE,WAAW,SAAS,EAAE,CAAC,GAAG,CAAA,GAAA,4GAAA,CAAA,yBAAsB,AAAD;IACrE,MAAM,EACJ,MAAM,eAAe,EACrB,OAAO,EACP,WAAW,gBAAgB,EAC5B,GAAG,CAAA,GAAA,4GAAA,CAAA,yBAAsB,AAAD,EAAE,QAAS;QAAE,MAAM,CAAC;IAAO;IAEpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO,OAAO;YAChB,UAAU;YACV,YAAY,EAAE;QAChB,OAAO;YACL,UAAU;YACV;QACF;IACF,GAAG;QAAC;QAAI;KAAQ;IAEhB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB,YACE,gBAAgB,QAAQ;QAK5B;IACF,GAAG;QAAC;KAAgB;IAEpB,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,QAAQ,IAAI,IAAI;QAErB,MAAM,cAAc;eACf;YACH;gBAAE,SAAS;gBAAS,MAAM;YAAiB;SAC5C;QACD,YAAY;QAEZ,IAAI,QAAQ;YACV,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,YAAY;gBAAE;gBAAS,SAAS;YAAO;YAC9D,IAAI,QAAQ,KAAK,QAAQ,EAAE;gBACzB,YAAY,CAAC,eAAiB;2BACzB;wBACH,KAAK,QAAQ;qBACd;YACH;QACF,OAAO;YACL,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,YAAY;gBAAE;YAAQ;YAC7C,IAAI,MAAM;gBACR,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK,OAAO,EAAE;YAC/C;QACF;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;AAEA,SAAS,SAAS,EAAE,QAAQ,EAA2B;IACrD,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;AAEA,SAAS;IACP,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG;IAChC,qBAAO,8OAAC,mIAAA,CAAA,UAAY;QAAC,UAAU;QAAU,WAAW;;;;;;AACtD;AAEA,SAAS;IACP,MAAM,EAAE,iBAAiB,EAAE,SAAS,EAAE,GAAG;IACzC,qBAAO,8OAAC,gIAAA,CAAA,UAAS;QAAC,eAAe;QAAmB,WAAW;;;;;;AACjE;AAEA,SAAS;IACP,MAAM,EAAE,gBAAgB,EAAE,GAAG;IAC7B,IAAI,CAAC,kBAAkB,OAAO;IAC9B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,kIAAA,CAAA,UAAW;;;;;;;;;;;;;;;AAIpB;AAEA,SAAS;IACP,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG;IAC7B,IAAI,SAAS,MAAM,GAAG,KAAK,QAAQ,OAAO;IAC1C,qBAAO,8OAAC,iIAAA,CAAA,UAAW;;;;;AACrB;AAGO,MAAM,OAAO,OAAO,MAAM,CAAC,UAAU;IAC1C,UAAU;IACV,UAAU;IACV,OAAO;IACP,SAAS;IACT,SAAS;AACX", "debugId": null}}, {"offset": {"line": 1042, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/app/dashboard/chat/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Chat } from \"@/components/chat/compound\";\r\n\r\nexport default function ChatPage() {\r\n  return (\r\n    <Chat.Provider>\r\n      <Chat>\r\n        <Chat.Loading />\r\n        <Chat.Options />\r\n        <Chat.Messages />\r\n        <Chat.Input />\r\n      </Chat>\r\n    </Chat.Provider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,qBACE,8OAAC,wIAAA,CAAA,OAAI,CAAC,QAAQ;kBACZ,cAAA,8OAAC,wIAAA,CAAA,OAAI;;8BACH,8OAAC,wIAAA,CAAA,OAAI,CAAC,OAAO;;;;;8BACb,8OAAC,wIAAA,CAAA,OAAI,CAAC,OAAO;;;;;8BACb,8OAAC,wIAAA,CAAA,OAAI,CAAC,QAAQ;;;;;8BACd,8OAAC,wIAAA,CAAA,OAAI,CAAC,KAAK;;;;;;;;;;;;;;;;AAInB", "debugId": null}}]}