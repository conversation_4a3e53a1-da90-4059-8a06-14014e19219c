{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/theme-toggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/theme-toggle.tsx <module evaluation>\",\n    \"ThemeToggle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,6DACA", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/theme-toggle.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/theme-toggle.tsx\",\n    \"ThemeToggle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,yCACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/app/page.tsx"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON> } from \"next\";\nimport Link from \"next/link\";\nimport { ThemeToggle } from \"@/components/theme-toggle\";\n\nexport const metadata: Metadata = {\n  title: \"Personal Assistance\",\n  description: \"Welcome to Personal Assistance!\",\n};\n\nexport default function Index() {\n  return (\n    <main className=\"min-h-screen flex flex-col items-center justify-center text-foreground px-4 relative\">\n      {/* Theme toggle in top right */}\n      <div className=\"absolute top-6 right-6 z-20\">\n        <ThemeToggle />\n      </div>\n      <div className=\"relative z-10 flex flex-col items-center\">\n        <div className=\"glass-intense p-8 mb-8 rounded-full glass-float glass-shimmer\">\n          <img\n            src=\"/robot.png\"\n            alt=\"Personal Assistant Robot\"\n            className=\"w-32 h-32 drop-shadow-[0_0_2rem_rgba(99,102,241,0.5)] animate-fade-in rounded-full\"\n            style={{ objectFit: 'cover' }}\n          />\n        </div>\n        <div className=\"glass-intense p-8 text-center max-w-2xl glass-shimmer\">\n          <h1 className=\"text-5xl font-bold mb-4 tracking-tighter bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600\">\n            Personal Assistance\n          </h1>\n          <p className=\"text-lg text-muted-foreground mb-8 max-w-md mx-auto\">\n            Your AI-powered assistant for productivity, organization, and creativity.<br />\n            <span className=\"text-primary font-medium\">Minimal. Fast. Reliable.</span>\n          </p>\n          <div className=\"flex flex-col gap-4 w-full max-w-xs mx-auto\">\n            <Link\n              href=\"/dashboard/chat/new\"\n              className=\"glass-intense bg-primary/80 text-primary-foreground rounded-lg py-3 px-6 font-semibold transition-all duration-300 ease-in-out text-center transform hover:scale-105 border-0 glass-shimmer\"\n            >\n              Try a Demo Chat\n            </Link>\n            <a\n              href=\"https://github.com/your-repo\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"text-sm text-muted-foreground underline text-center hover:text-primary transition-colors\"\n            >\n              Learn more on GitHub\n            </a>\n          </div>\n        </div>\n        <footer className=\"mt-8 text-xs text-muted-foreground opacity-70\">\n          &copy; {new Date().getFullYear()} Personal Assistance. All rights reserved.\n        </footer>\n      </div>\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAK,WAAU;;0BAEd,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,8HAAA,CAAA,cAAW;;;;;;;;;;0BAEd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,KAAI;4BACJ,KAAI;4BACJ,WAAU;4BACV,OAAO;gCAAE,WAAW;4BAAQ;;;;;;;;;;;kCAGhC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmH;;;;;;0CAGjI,8OAAC;gCAAE,WAAU;;oCAAsD;kDACQ,8OAAC;;;;;kDAC1E,8OAAC;wCAAK,WAAU;kDAA2B;;;;;;;;;;;;0CAE7C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAKL,8OAAC;wBAAO,WAAU;;4BAAgD;4BACxD,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;AAK3C", "debugId": null}}]}