{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/theme-toggle.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON>, <PERSON>, Monitor } from \"lucide-react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { useTheme } from \"./theme-provider\";\n\nexport function ThemeToggle() {\n  const { setTheme, theme } = useTheme();\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button \n          variant=\"ghost\" \n          size=\"sm\" \n          className=\"glass-button h-9 w-9 p-0 hover:bg-transparent\"\n        >\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent \n        align=\"end\" \n        className=\"glass-card border-0 shadow-2xl\"\n      >\n        <DropdownMenuItem \n          onClick={() => setTheme(\"light\")}\n          className=\"cursor-pointer hover:bg-white/10 focus:bg-white/10\"\n        >\n          <Sun className=\"mr-2 h-4 w-4\" />\n          <span>Light</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem \n          onClick={() => setTheme(\"dark\")}\n          className=\"cursor-pointer hover:bg-white/10 focus:bg-white/10\"\n        >\n          <Moon className=\"mr-2 h-4 w-4\" />\n          <span>Dark</span>\n        </DropdownMenuItem>\n        <DropdownMenuItem \n          onClick={() => setTheme(\"system\")}\n          className=\"cursor-pointer hover:bg-white/10 focus:bg-white/10\"\n        >\n          <Monitor className=\"mr-2 h-4 w-4\" />\n          <span>System</span>\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AAMA;;;AAVA;;;;;AAYO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD;IAEnC,qBACE,6LAAC,wIAAA,CAAA,eAAY;;0BACX,6LAAC,wIAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,8HAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;;sCAEV,6LAAC,mMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,6LAAC,qMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,6LAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,wIAAA,CAAA,sBAAmB;gBAClB,OAAM;gBACN,WAAU;;kCAEV,6LAAC,wIAAA,CAAA,mBAAgB;wBACf,SAAS,IAAM,SAAS;wBACxB,WAAU;;0CAEV,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,6LAAC;0CAAK;;;;;;;;;;;;kCAER,6LAAC,wIAAA,CAAA,mBAAgB;wBACf,SAAS,IAAM,SAAS;wBACxB,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;0CAAK;;;;;;;;;;;;kCAER,6LAAC,wIAAA,CAAA,mBAAgB;wBACf,SAAS,IAAM,SAAS;wBACxB,WAAU;;0CAEV,6LAAC,2MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;;AAKhB;GA5CgB;;QACc,mIAAA,CAAA,WAAQ;;;KADtB", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/components/Header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { SidebarTrigger, useSidebar } from \"./ui/sidebar\";\r\nimport { Button } from \"./ui/button\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { IoMdAdd } from \"react-icons/io\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { ThemeToggle } from \"./theme-toggle\";\r\n\r\nexport default function Header() {\r\n  const router = useRouter();\r\n  const { state } = useSidebar();\r\n\r\n  const handleNewChat = () => {\r\n    router.push(\"/dashboard/chat/new\");\r\n  };\r\n\r\n  return (\r\n    <header\r\n      className={cn([\r\n        \"glass-header py-2 px-4 flex items-center justify-between text-foreground fixed z-50 h-14 transition-all duration-200 ease-linear w-full backdrop-blur-md\",\r\n        state === \"expanded\"\r\n          ? \"md:w-[calc(100vw-248px)]\"\r\n          : \"md:w-[calc(100vw-40px)]\",\r\n      ])}\r\n    >\r\n      <SidebarTrigger className=\"glass-button border-0\" />\r\n      <div className=\"flex items-center gap-3\">\r\n        <Button\r\n          variant=\"outline\"\r\n          onClick={handleNewChat}\r\n          className=\"glass-button flex items-center gap-2 text-foreground border-0 hover:bg-transparent\"\r\n        >\r\n          <IoMdAdd className=\"h-5 w-5\" />\r\n          New Chat\r\n        </Button>\r\n        <ThemeToggle />\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,aAAU,AAAD;IAE3B,MAAM,gBAAgB;QACpB,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE;YACZ;YACA,UAAU,aACN,6BACA;SACL;;0BAED,6LAAC,+HAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,8HAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS;wBACT,WAAU;;0CAEV,6LAAC,iJAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGjC,6LAAC,iIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;AAIpB;GA/BwB;;QACP,qIAAA,CAAA,YAAS;QACN,+HAAA,CAAA,aAAU;;;KAFN", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work-profile/personal-assistant/chat_ui/app/dashboard/layout.tsx"], "sourcesContent": ["\"use client\"\r\nimport Header from \"@/components/Header\";\r\n\r\nexport default function DashboardLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  return (\r\n    <div className=\"relative \">\r\n      <Header />\r\n      <div className=\"pt-14 \">\r\n        {children}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAGe,SAAS,gBAAgB,KAItC;QAJsC,EACtC,QAAQ,EAGR,GAJsC;IAKtC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,wHAAA,CAAA,UAAM;;;;;0BACP,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;KAbwB", "debugId": null}}]}